import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:intl/intl.dart' as intl;

class NewsTab extends StatefulWidget {
  const NewsTab({this.isInMarketDetails = false});
  final bool isInMarketDetails;

  @override
  State<NewsTab> createState() => _NewsTabState();
}

class _NewsTabState extends State<NewsTab> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _textController;
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final position = _scrollController.position;
    final threshold = position.maxScrollExtent - 600;

    if (position.pixels >= threshold) {
      final newsBloc = context.read<NewsBloc>();
      final state = newsBloc.state;

      if (!state.hasReachedMax &&
          state.processState != const NewsProcessState.loadingMore()) {
        newsBloc.add(NewsEvent.fetchNews(loadMore: true));
      }
    }
  }

  List<_NewsSection> _groupNewsForStickyHeaders(
    List<Object?> groupedNews,
    EquitiLocalization l10n,
  ) {
    final List<_NewsSection> sections = [];
    String? currentHeader;
    List<NewsItem> currentItems = [];

    for (final item in groupedNews) {
      if (item is DayPart || item is DateTime) {
        // Save previous section if it exists
        if (currentHeader != null && currentItems.isNotEmpty) {
          sections.add(
            _NewsSection(
              header: currentHeader,
              items: List<NewsItem>.of(currentItems),
            ),
          );
          currentItems.clear();
        }

        // Set new header
        if (item is DayPart) {
          currentHeader = DateFormatter.localizedDay(item, l10n);
        } else if (item is DateTime) {
          currentHeader = intl.DateFormat('MMM d, yyyy').format(item);
        }
      } else if (item is NewsItem) {
        currentItems.add(item);
      }
    }

    // Add the last section
    if (currentHeader != null && currentItems.isNotEmpty) {
      sections.add(
        _NewsSection(
          header: currentHeader,
          items: List<NewsItem>.of(currentItems),
        ),
      );
    }

    return sections;
  }

  Widget _buildStickyHeaderContent(
    BuildContext context,
    NewsState state,
    EquitiLocalization l10n,
    Locale locale,
  ) {
    final sections = _groupNewsForStickyHeaders(state.groupedNews, l10n);

    if (sections.isEmpty) {
      return SliverFillRemaining(
        hasScrollBody: false,
        child: EmptyOrErrorStateComponent.empty(
          title: l10n.trader_nothingToShow,
          description: l10n.trader_noResultsDescription,
          svgImage: trader.Assets.images.emptySearch.svg(
            allowDrawingOutsideViewBox: true,
          ),
        ),
      );
    }

    return SliverMainAxisGroup(
      slivers: [
        // Sticky headers with news items
        ...sections.map(
          (section) => SliverStickyHeader(
            header: Container(
              color: context.duploTheme.background.bgSecondary,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              alignment:
                  intl.Bidi.isRtlLanguage(locale.languageCode)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
              width: double.infinity,
              child: DuploText(
                textAlign: TextAlign.start,
                text: section.header,
                style: context.duploTextStyles.textMd,
                fontWeight: DuploFontWeight.semiBold,
                color: context.duploTheme.text.textPrimary,
              ),
            ),
            sliver: SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((ctx, index) {
                  if (index >= section.items.length) return null;
                  final newsItem = section.items.elementAtOrNull(index);
                  if (newsItem == null) return null;
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom: index < section.items.length - 1 ? 16.0 : 0.0,
                    ),
                    child: NewsListItem(newsItemDetails: newsItem.item),
                  );
                }, childCount: section.items.length),
              ),
            ),
          ),
        ),

        if (!state.hasReachedMax)
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: switch (state.processState) {
                  NewsLoadingMore() => CircularProgressIndicator.adaptive(),
                  _ => const SizedBox(
                    height: 50,
                  ), // Space to trigger scroll detection
                },
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context);

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        BlocBuilder<NewsBloc, NewsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            return switch (state.processState) {
              NewsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              NewsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<NewsBloc>().add(
                      NewsEvent.fetchNews(),
                    );
                  },
                ),
              ),
              _ => _buildStickyHeaderContent(
                blocBuilderContext,
                state,
                l10n,
                locale,
              ),
            };
          },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

/// Helper class to group news items by date sections for sticky headers
class _NewsSection {
  final String header;
  final List<NewsItem> items;

  const _NewsSection({required this.header, required this.items});
}
